<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">


<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    
    <title>Charts &#8212; phpMyAdmin 5.0.1 documentation</title>
    
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    
    <script type="text/javascript">
      var DOCUMENTATION_OPTIONS = {
        URL_ROOT:    './',
        VERSION:     '5.0.1',
        COLLAPSE_INDEX: false,
        FILE_SUFFIX: '.html',
        HAS_SOURCE:  true
      };
    </script>
    <script type="text/javascript" src="_static/jquery.js"></script>
    <script type="text/javascript" src="_static/underscore.js"></script>
    <script type="text/javascript" src="_static/doctools.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="top" title="phpMyAdmin 5.0.1 documentation" href="index.html" />
    <link rel="up" title="User Guide" href="user.html" />
    <link rel="next" title="Import and export" href="import_export.html" />
    <link rel="prev" title="Relations" href="relations.html" /> 
  </head>
  <body role="document">
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="import_export.html" title="Import and export"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="relations.html" title="Relations"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.0.1 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="user.html" accesskey="U">User Guide</a> &#187;</li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="charts">
<span id="id1"></span><h1>Charts<a class="headerlink" href="#charts" title="Permalink to this headline">¶</a></h1>
<div class="versionadded">
<p><span class="versionmodified">New in version 3.4.0.</span></p>
</div>
<p>Since phpMyAdmin version 3.4.0, you can easily generate charts from a SQL query
by clicking the &#8220;Display chart&#8221; link in the &#8220;Query results operations&#8221; area.</p>
<img alt="_images/query_result_operations.png" src="_images/query_result_operations.png" />
<p>A window layer &#8220;Display chart&#8221; is shown in which you can customize the chart with the following options.</p>
<ul class="simple">
<li>Chart type: Allows you to choose the type of chart. Supported types are bar charts, column charts, line charts, spline charts, area charts, pie charts and timeline charts (only the chart types applicable for current series selection are offered).</li>
<li>X-axis: Allows to choose the field for the main axis.</li>
<li>Series: Allows to choose series for the chart. You can choose multiple series.</li>
<li>Title: Allows specifying a title for the chart which is displayed above the chart.</li>
<li>X-axis and Y-axis labels: Allows specifying labels for axes.</li>
<li>Start row and a number of rows: Allows generating charts only for a specified number of rows of the results set.</li>
</ul>
<img alt="_images/chart.png" src="_images/chart.png" />
<div class="section" id="chart-implementation">
<h2>Chart implementation<a class="headerlink" href="#chart-implementation" title="Permalink to this headline">¶</a></h2>
<p>Charts in phpMyAdmin are drawn using <a class="reference external" href="http://www.jqplot.com/">jqPlot</a> jQuery library.</p>
</div>
<div class="section" id="examples">
<h2>Examples<a class="headerlink" href="#examples" title="Permalink to this headline">¶</a></h2>
<div class="section" id="pie-chart">
<h3>Pie chart<a class="headerlink" href="#pie-chart" title="Permalink to this headline">¶</a></h3>
<p>Query results for a simple pie chart can be generated with:</p>
<div class="highlight-mysql"><div class="highlight"><pre><span></span><span class="k">SELECT</span> <span class="s1">&#39;Food&#39;</span> <span class="k">AS</span> <span class="s1">&#39;expense&#39;</span><span class="p">,</span>
   <span class="mi">1250</span> <span class="k">AS</span> <span class="s1">&#39;amount&#39;</span> <span class="k">UNION</span>
<span class="k">SELECT</span> <span class="s1">&#39;Accommodation&#39;</span><span class="p">,</span> <span class="mi">500</span> <span class="k">UNION</span>
<span class="k">SELECT</span> <span class="s1">&#39;Travel&#39;</span><span class="p">,</span> <span class="mi">720</span> <span class="k">UNION</span>
<span class="k">SELECT</span> <span class="s1">&#39;Misc&#39;</span><span class="p">,</span> <span class="mi">220</span>
</pre></div>
</div>
<p>And the result of this query is:</p>
<table border="1" class="docutils">
<colgroup>
<col width="65%" />
<col width="35%" />
</colgroup>
<thead valign="bottom">
<tr class="row-odd"><th class="head">expense</th>
<th class="head">amount</th>
</tr>
</thead>
<tbody valign="top">
<tr class="row-even"><td>Food</td>
<td>1250</td>
</tr>
<tr class="row-odd"><td>Accommodation</td>
<td>500</td>
</tr>
<tr class="row-even"><td>Travel</td>
<td>720</td>
</tr>
<tr class="row-odd"><td>Misc</td>
<td>220</td>
</tr>
</tbody>
</table>
<p>Choosing expense as the X-axis and amount in series:</p>
<img alt="_images/pie_chart.png" src="_images/pie_chart.png" />
</div>
<div class="section" id="bar-and-column-chart">
<h3>Bar and column chart<a class="headerlink" href="#bar-and-column-chart" title="Permalink to this headline">¶</a></h3>
<p>Both bar charts and column chats support stacking. Upon selecting one of these types a checkbox is displayed to select stacking.</p>
<p>Query results for a simple bar or column chart can be generated with:</p>
<div class="highlight-mysql"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
   <span class="s1">&#39;ACADEMY DINOSAUR&#39;</span> <span class="k">AS</span> <span class="s1">&#39;title&#39;</span><span class="p">,</span>
   <span class="mi">0</span><span class="p">.</span><span class="mi">99</span> <span class="k">AS</span> <span class="s1">&#39;rental_rate&#39;</span><span class="p">,</span>
   <span class="mi">20</span><span class="p">.</span><span class="mi">99</span> <span class="k">AS</span> <span class="s1">&#39;replacement_cost&#39;</span> <span class="k">UNION</span>
<span class="k">SELECT</span> <span class="s1">&#39;ACE GOLDFINGER&#39;</span><span class="p">,</span> <span class="mi">4</span><span class="p">.</span><span class="mi">99</span><span class="p">,</span> <span class="mi">12</span><span class="p">.</span><span class="mi">99</span> <span class="k">UNION</span>
<span class="k">SELECT</span> <span class="s1">&#39;ADAPTATION HOLES&#39;</span><span class="p">,</span> <span class="mi">2</span><span class="p">.</span><span class="mi">99</span><span class="p">,</span> <span class="mi">18</span><span class="p">.</span><span class="mi">99</span> <span class="k">UNION</span>
<span class="k">SELECT</span> <span class="s1">&#39;AFFAIR PREJUDICE&#39;</span><span class="p">,</span> <span class="mi">2</span><span class="p">.</span><span class="mi">99</span><span class="p">,</span> <span class="mi">26</span><span class="p">.</span><span class="mi">99</span> <span class="k">UNION</span>
<span class="k">SELECT</span> <span class="s1">&#39;AFRICAN EGG&#39;</span><span class="p">,</span> <span class="mi">2</span><span class="p">.</span><span class="mi">99</span><span class="p">,</span> <span class="mi">22</span><span class="p">.</span><span class="mi">99</span>
</pre></div>
</div>
<p>And the result of this query is:</p>
<table border="1" class="docutils">
<colgroup>
<col width="35%" />
<col width="27%" />
<col width="37%" />
</colgroup>
<thead valign="bottom">
<tr class="row-odd"><th class="head">title</th>
<th class="head">rental_rate</th>
<th class="head">replacement_cost</th>
</tr>
</thead>
<tbody valign="top">
<tr class="row-even"><td>ACADEMY DINOSAUR</td>
<td>0.99</td>
<td>20.99</td>
</tr>
<tr class="row-odd"><td>ACE GOLDFINGER</td>
<td>4.99</td>
<td>12.99</td>
</tr>
<tr class="row-even"><td>ADAPTATION HOLES</td>
<td>2.99</td>
<td>18.99</td>
</tr>
<tr class="row-odd"><td>AFFAIR PREJUDICE</td>
<td>2.99</td>
<td>26.99</td>
</tr>
<tr class="row-even"><td>AFRICAN EGG</td>
<td>2.99</td>
<td>22.99</td>
</tr>
</tbody>
</table>
<p>Choosing title as the X-axis and rental_rate and replacement_cost as series:</p>
<img alt="_images/column_chart.png" src="_images/column_chart.png" />
</div>
<div class="section" id="scatter-chart">
<h3>Scatter chart<a class="headerlink" href="#scatter-chart" title="Permalink to this headline">¶</a></h3>
<p>Scatter charts are useful in identifying the movement of one or more variable(s) compared to another variable.</p>
<p>Using the same data set from bar and column charts section and choosing replacement_cost as the X-axis and rental_rate in series:</p>
<img alt="_images/scatter_chart.png" src="_images/scatter_chart.png" />
</div>
<div class="section" id="line-spline-and-timeline-charts">
<h3>Line, spline and timeline charts<a class="headerlink" href="#line-spline-and-timeline-charts" title="Permalink to this headline">¶</a></h3>
<p>These charts can be used to illustrate trends in underlying data. Spline charts draw smooth lines while timeline charts draw X-axis taking the distances between the dates/time into consideration.</p>
<p>Query results for a simple line, spline or timeline chart can be generated with:</p>
<div class="highlight-mysql"><div class="highlight"><pre><span></span><span class="k">SELECT</span>
   <span class="kt">DATE</span><span class="p">(</span><span class="s1">&#39;2006-01-08&#39;</span><span class="p">)</span> <span class="k">AS</span> <span class="s1">&#39;date&#39;</span><span class="p">,</span>
   <span class="mi">2056</span> <span class="k">AS</span> <span class="s1">&#39;revenue&#39;</span><span class="p">,</span>
   <span class="mi">1378</span> <span class="k">AS</span> <span class="s1">&#39;cost&#39;</span> <span class="k">UNION</span>
<span class="k">SELECT</span> <span class="kt">DATE</span><span class="p">(</span><span class="s1">&#39;2006-01-09&#39;</span><span class="p">),</span> <span class="mi">1898</span><span class="p">,</span> <span class="mi">2301</span> <span class="k">UNION</span>
<span class="k">SELECT</span> <span class="kt">DATE</span><span class="p">(</span><span class="s1">&#39;2006-01-15&#39;</span><span class="p">),</span> <span class="mi">1560</span><span class="p">,</span> <span class="mi">600</span> <span class="k">UNION</span>
<span class="k">SELECT</span> <span class="kt">DATE</span><span class="p">(</span><span class="s1">&#39;2006-01-17&#39;</span><span class="p">),</span> <span class="mi">3457</span><span class="p">,</span> <span class="mi">1565</span>
</pre></div>
</div>
<p>And the result of this query is:</p>
<table border="1" class="docutils">
<colgroup>
<col width="44%" />
<col width="33%" />
<col width="22%" />
</colgroup>
<thead valign="bottom">
<tr class="row-odd"><th class="head">date</th>
<th class="head">revenue</th>
<th class="head">cost</th>
</tr>
</thead>
<tbody valign="top">
<tr class="row-even"><td>2016-01-08</td>
<td>2056</td>
<td>1378</td>
</tr>
<tr class="row-odd"><td>2006-01-09</td>
<td>1898</td>
<td>2301</td>
</tr>
<tr class="row-even"><td>2006-01-15</td>
<td>1560</td>
<td>600</td>
</tr>
<tr class="row-odd"><td>2006-01-17</td>
<td>3457</td>
<td>1565</td>
</tr>
</tbody>
</table>
<img alt="_images/line_chart.png" src="_images/line_chart.png" />
<img alt="_images/spline_chart.png" src="_images/spline_chart.png" />
<img alt="_images/timeline_chart.png" src="_images/timeline_chart.png" />
</div>
</div>
</div>


          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h3><a href="index.html">Table Of Contents</a></h3>
  <ul>
<li><a class="reference internal" href="#">Charts</a><ul>
<li><a class="reference internal" href="#chart-implementation">Chart implementation</a></li>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#pie-chart">Pie chart</a></li>
<li><a class="reference internal" href="#bar-and-column-chart">Bar and column chart</a></li>
<li><a class="reference internal" href="#scatter-chart">Scatter chart</a></li>
<li><a class="reference internal" href="#line-spline-and-timeline-charts">Line, spline and timeline charts</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  <h4>Previous topic</h4>
  <p class="topless"><a href="relations.html"
                        title="previous chapter">Relations</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="import_export.html"
                        title="next chapter">Import and export</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/charts.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3>Quick search</h3>
    <form class="search" action="search.html" method="get">
      <div><input type="text" name="q" /></div>
      <div><input type="submit" value="Go" /></div>
      <input type="hidden" name="check_keywords" value="yes" />
      <input type="hidden" name="area" value="default" />
    </form>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="import_export.html" title="Import and export"
             >next</a> |</li>
        <li class="right" >
          <a href="relations.html" title="Relations"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.0.1 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="user.html" >User Guide</a> &#187;</li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2018, The phpMyAdmin devel team.
      Created using <a href="http://sphinx-doc.org/">Sphinx</a> 1.4.9.
    </div>
  </body>
</html>