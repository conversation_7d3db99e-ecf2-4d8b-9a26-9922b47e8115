<div class="floatleft">
    {% if type == 'row' %}
        {% trans 'Add/Delete criteria rows' %}:
    {% else %}
        {% trans 'Add/Delete columns' %}:
    {% endif %}
    <select size="1" name="{{ type == 'row' ? 'criteriaRowAdd' : 'criteriaColumnAdd' }}">
        <option value="-3">-3</option>
        <option value="-2">-2</option>
        <option value="-1">-1</option>
        <option value="0" selected="selected">0</option>
        <option value="1">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
    </select>
</div>
