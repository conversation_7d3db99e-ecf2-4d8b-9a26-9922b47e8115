<select name="field_virtuality[{{ column_number }}]"
    id="field_{{ column_number }}_{{ ci - ci_offset }}"
    class="virtuality">
    {% for key, value in options %}
        <option value="{{ key }}"
            {% set key_length = key|length %}
            {%- if column_meta['Extra'] is defined
                and key != ''
                and column_meta['Extra']|slice(0,key_length) is same as (key) %}
                selected="selected"
            {%- endif %}>
            {{ value }}
        </option>
    {% endfor %}
</select>

{% if char_editing == 'textarea' %}
    {% apply spaceless %}
    <textarea name="field_expression[{{ column_number }}]"
        cols="15"
        class="textfield expression">
        {{ expression }}
    </textarea>
    {% endapply %}
{% else %}
    <input type="text"
        name="field_expression[{{ column_number }}]"
        size="12"
        value="{{ expression }}"
        placeholder="{% trans 'Expression' %}"
        class="textfield expression">
{% endif %}
