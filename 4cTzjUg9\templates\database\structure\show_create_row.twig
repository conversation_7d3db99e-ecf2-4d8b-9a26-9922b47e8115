<fieldset>
    <legend>{{ title }}</legend>
    <table class="show_create">
        <thead>
        <tr>
            <th>{{ raw_title }}</th>
            <th>{{ 'Create %s'|trans|format(raw_title) }}</th>
        </tr>
        </thead>
        <tbody>
        {% for object in db_objects %}
            <tr>
                <td><strong>{{ object|mime_default_function }}</strong></td>
                <td>{{ dbi.getTable(db, object).showCreate()|mime_default_function }}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
</fieldset>
