{# Tracked tables exists#}
{% if head_version_exists %}
    <div id="tracked_tables">
        <h3>{% trans 'Tracked tables' %}</h3>

        <form method="post" action="db_tracking.php" name="trackedForm"
            id="trackedForm" class="ajax">
            {{ get_hidden_inputs(db) }}
            <table id="versions" class="data">
                <thead>
                    <tr>
                        <th></th>
                        <th>{% trans 'Table' %}</th>
                        <th>{% trans 'Last version' %}</th>
                        <th>{% trans 'Created' %}</th>
                        <th>{% trans 'Updated' %}</th>
                        <th>{% trans 'Status' %}</th>
                        <th>{% trans 'Action' %}</th>
                        <th>{% trans 'Show' %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for version in versions %}
                        <tr>
                            <td class="center">
                                <input type="checkbox" name="selected_tbl[]"
                                    class="checkall" id="selected_tbl_{{ version.table_name }}"
                                    value="{{ version.table_name }}">
                            </td>
                            <th>
                                <label for="selected_tbl_{{ version.table_name }}">
                                    {{ version.table_name }}
                                </label>
                            </th>
                            <td class="right">
                                {{ version.version }}
                            </td>
                            <td>
                                {{ version.date_created }}
                            </td>
                            <td>
                                {{ version.date_updated }}
                            </td>
                            <td>
                                {{ version.status_button|raw }}
                            </td>
                            <td>
                                <a class="delete_tracking_anchor ajax" href="db_tracking.php" data-post="
                                    {{- get_common({
                                        'db': db,
                                        'goto': 'tbl_tracking.php',
                                        'back': 'db_tracking.php',
                                        'table': version.table_name,
                                        'delete_tracking': true
                                    }, '') }}">
                                    {{ get_icon('b_drop', 'Delete tracking'|trans) }}
                                </a>
                            </td>
                            <td>
                                <a href="tbl_tracking.php" data-post="
                                    {{- get_common({
                                        'db': db,
                                        'goto': 'tbl_tracking.php',
                                        'back': 'db_tracking.php',
                                        'table': version.table_name
                                    }, '') }}">
                                    {{ get_icon('b_versions', 'Versions'|trans) }}
                                </a>
                                <a href="tbl_tracking.php" data-post="
                                    {{- get_common({
                                        'db': db,
                                        'goto': 'tbl_tracking.php',
                                        'back': 'db_tracking.php',
                                        'table': version.table_name,
                                        'report': true,
                                        'version': version.version
                                    }, '') }}">
                                    {{ get_icon('b_report', 'Tracking report'|trans) }}
                                </a>
                                <a href="tbl_tracking.php" data-post="
                                    {{- get_common({
                                        'db': db,
                                        'goto': 'tbl_tracking.php',
                                        'back': 'db_tracking.php',
                                        'table': version.table_name,
                                        'snapshot': true,
                                        'version': version.version
                                    }, '') }}">
                                    {{ get_icon('b_props', 'Structure snapshot'|trans) }}
                                </a>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% include 'select_all.twig' with {
                'pma_theme_image': pma_theme_image,
                'text_dir': text_dir,
                'form_name': 'trackedForm'
            } only %}
            {{ get_button_or_image(
                'submit_mult',
                'mult_submit',
                'Delete tracking'|trans,
                'b_drop',
                'delete_tracking'
            ) }}
        </form>
    </div>
{% endif %}
{% if untracked_tables_exists %}
    <h3>{% trans 'Untracked tables' %}</h3>
    <form method="post" action="db_tracking.php" name="untrackedForm"
        id="untrackedForm" class="ajax">
        {{ get_hidden_inputs(db) }}
        <table id="noversions" class="data">
            <thead>
                <tr>
                    <th></th>
                    <th>{% trans 'Table' %}</th>
                    <th>{% trans 'Action' %}</th>
                </tr>
            </thead>
            <tbody>
                {% for table_name in untracked_tables %}
                  {% if get_tracker_version(db, table_name) == -1 %}
                    <tr>
                        <td class="center">
                            <input type="checkbox" name="selected_tbl[]"
                                class="checkall" id="selected_tbl_{{ table_name }}"
                                value="{{ table_name }}">
                        </td>
                        <th>
                            <label for="selected_tbl_{{ table_name }}">
                                {{ table_name }}
                            </label>
                        </th>
                        <td>
                            <a href="tbl_tracking.php{{ url_query|raw }}&amp;table={{ table_name }}">
                                {{ get_icon('eye', 'Track table'|trans) }}
                            </a>
                        </td>
                    </tr>
                  {% endif %}
                {% endfor %}
            </tbody>
        </table>
        {% include 'select_all.twig' with {
            'pma_theme_image': pma_theme_image,
            'text_dir': text_dir,
            'form_name': 'untrackedForm'
        } only %}
        {{ get_button_or_image(
            'submit_mult',
            'mult_submit',
            'Track table'|trans,
            'eye',
            'track'
        ) }}
    </form>
{% endif %}
