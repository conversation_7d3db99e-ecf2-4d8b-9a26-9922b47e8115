<form action="db_designer.php" method="post" name="save_as_pages" id="save_as_pages" class="ajax">
    {{ get_hidden_inputs(db) }}
    <fieldset id="page_save_as_options">
        <table>
            <tbody>
                <tr>
                    <td>
                        <input type="hidden" name="operation" value="savePage">
                        {% include 'database/designer/page_selector.twig' with {
                            'pdfwork': pdfwork,
                            'pages': pages
                        } only %}
                    </td>
                </tr>
                <tr>
                    <td>
                        {{ get_radio_fields(
                            'save_page',
                            {
                                'same': 'Save to selected page'|trans,
                                'new': 'Create a page and save to it'|trans
                            },
                            'same',
                            true
                        ) }}
                    </td>
                </tr>
                <tr>
                    <td>
                        <label for="selected_value">{% trans 'New page name' %}</label>
                        <input type="text" name="selected_value" id="selected_value">
                    </td>
                </tr>
            </tbody>
        </table>
    </fieldset>
</form>
