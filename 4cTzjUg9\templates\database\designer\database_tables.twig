{% for designerTable in tables %}
    {% set i = loop.index0 %}
    {% set t_n_url = designerTable.getDbTableString()|escape('url') %}
    {% set db = designerTable.getDatabaseName() %}
    {% set db_url = db|escape('url') %}
    {% set t_n = designerTable.getDbTableString() %}
    {% set table_name = designerTable.getTableName()|escape('html') %}
    <input name="t_x[{{ t_n_url }}]" type="hidden" id="t_x_{{ t_n_url }}_" />
    <input name="t_y[{{ t_n_url }}]" type="hidden" id="t_y_{{ t_n_url }}_" />
    <input name="t_v[{{ t_n_url }}]" type="hidden" id="t_v_{{ t_n_url }}_" />
    <input name="t_h[{{ t_n_url }}]" type="hidden" id="t_h_{{ t_n_url }}_" />
    <table id="{{ t_n_url }}"
        db_url="{{ designerTable.getDatabaseName()|escape('url') }}"
        table_name_url="{{ designerTable.getTableName()|escape('url') }}"
        cellpadding="0"
        cellspacing="0"
        class="designer_tab"
        style="position:absolute; left:
            {{- tab_pos[t_n] is defined ? tab_pos[t_n]['X'] : random(range(20, 700)) }}px; top:
            {{- tab_pos[t_n] is defined ? tab_pos[t_n]['Y'] : random(range(20, 550)) }}px; display:
            {{- tab_pos[t_n] is defined or display_page == -1 ? 'block' : 'none' }}; z-index: 1;"> <!--"-->
        <thead>
            <tr class="header">
                {% if has_query %}
                    <td class="select_all">
                        <input class="select_all_1"
                            type="checkbox"
                            style="margin: 0;"
                            value="select_all_{{ t_n_url }}"
                            id="select_all_{{ i }}"
                            title="{% trans 'Select all' %}"
                            table_name="{{ table_name }}"
                            db_name="{{ db }}">
                    </td>
                {% endif %}
                <td class="small_tab"
                    title="{% trans 'Show/hide columns' %}"
                    id="id_hide_tbody_{{ t_n_url }}"
                    table_name="{{ t_n_url }}">{{ tab_pos[t_n] is not defined or tab_pos[t_n]['V'] is not empty ? 'v' : '&gt;' }}</td>
                <td class="small_tab_pref small_tab_pref_1"
                    db="{{ designerTable.getDatabaseName() }}"
                    db_url="{{ designerTable.getDatabaseName()|escape('url') }}"
                    table_name="{{ designerTable.getTableName() }}"
                    table_name_url="{{ designerTable.getTableName()|escape('url') }}">
                    <img src="{{ theme.getImgPath('designer/exec_small.png') }}"
                        title="{% trans 'See table structure' %}">
                </td>
                <td id="id_zag_{{ t_n_url }}"
                    class="tab_zag nowrap tab_zag_noquery"
                    table_name="{{ t_n_url }}"
                    query_set="{{ has_query ? 1 : 0 }}">
                    <span class="owner">{{ designerTable.getDatabaseName() }}</span>
                    {{ designerTable.getTableName() }}
                </td>
                {% if has_query %}
                    <td class="tab_zag tab_zag_query"
                        id="id_zag_{{ t_n_url }}_2"
                        table_name="{{ t_n_url }}">
                    </td>
               {% endif %}
            </tr>
        </thead>
        <tbody id="id_tbody_{{ t_n_url }}"
            {{- tab_pos[t_n] is defined and tab_pos[t_n]['V'] is empty ? ' style="display: none"' }}>
            {% set display_field = designerTable.getDisplayField() %}
            {% for j in 0..tab_column[t_n]['COLUMN_ID']|length - 1 %}
                {% set col_name = tab_column[t_n]['COLUMN_NAME'][j] %}
                {% set tmp_column = t_n ~ '.' ~ tab_column[t_n]['COLUMN_NAME'][j] %}
                {% set click_field_param = [
                    designerTable.getTableName()|escape('url'),
                    tab_column[t_n]['COLUMN_NAME'][j]|url_encode
                ] %}
                {% if not designerTable.supportsForeignkeys() %}
                    {% set click_field_param = click_field_param|merge([tables_pk_or_unique_keys[tmp_column] is defined ? 1 : 0]) %}
                {% else %}
                    {# if foreign keys are supported, it's not necessary that the
                    index is a primary key #}
                    {% set click_field_param = click_field_param|merge([tables_all_keys[tmp_column] is defined ? 1 : 0]) %}
                {% endif %}
                {% set click_field_param = click_field_param|merge([db]) %}
                <tr id="id_tr_{{ designerTable.getTableName()|escape('url') }}.{{ tab_column[t_n]['COLUMN_NAME'][j] }}" class="tab_field
                    {{- display_field == tab_column[t_n]['COLUMN_NAME'][j] ? '_3' }}" click_field_param="
                    {{- click_field_param|join(',') }}">
                    {% if has_query %}
                        <td class="select_all">
                            <input class="select_all_store_col"
                                value="{{ t_n_url }}{{ tab_column[t_n]['COLUMN_NAME'][j]|url_encode }}"
                                type="checkbox"
                                id="select_{{ t_n_url }}._{{ tab_column[t_n]['COLUMN_NAME'][j]|url_encode }}"
                                style="margin: 0;"
                                title="{{ 'Select "%s"'|trans|format(col_name) }}"
                                id_check_all="select_all_{{ i }}"
                                db_name="{{ db }}"
                                table_name="{{ table_name }}"
                                col_name="{{ col_name }}">
                        </td>
                    {% endif %}
                    <td width="10px" colspan="3" id="{{ t_n_url }}.
                        {{- tab_column[t_n]['COLUMN_NAME'][j]|url_encode }}">
                        <div class="nowrap">
                            {% set type = columns_type[t_n ~ '.' ~ tab_column[t_n]['COLUMN_NAME'][j]] %}
                            <img src="{{ theme.getImgPath(type) }}.png" alt="*">
                            {{ tab_column[t_n]['COLUMN_NAME'][j] }} : {{ tab_column[t_n]['TYPE'][j] }}
                        </div>
                    </td>
                    {% if has_query %}
                        <td class="small_tab_pref small_tab_pref_click_opt"
                            {# Escaped 2 times to be able to use it in innerHtml #}
                            option_col_name_modal="<strong>{{ 'Add an option for column "%s".'|trans|format(col_name)|escape('html')|escape('html') }}</strong>"
                            db_name="{{ db }}"
                            table_name="{{ table_name }}"
                            col_name="{{ col_name }}"
                            db_table_name_url="{{ t_n_url }}">
                            <img src="{{ theme.getImgPath('designer/exec_small.png') }}" title="{% trans 'Options'  %}" />
                        </td>
                    {% endif %}
                </tr>
            {% endfor %}
        </tbody>
    </table>
{% endfor %}
