<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">


<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    
    <title>Transformations &#8212; phpMyAdmin 5.0.1 documentation</title>
    
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    
    <script type="text/javascript">
      var DOCUMENTATION_OPTIONS = {
        URL_ROOT:    './',
        VERSION:     '5.0.1',
        COLLAPSE_INDEX: false,
        FILE_SUFFIX: '.html',
        HAS_SOURCE:  true
      };
    </script>
    <script type="text/javascript" src="_static/jquery.js"></script>
    <script type="text/javascript" src="_static/underscore.js"></script>
    <script type="text/javascript" src="_static/doctools.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="top" title="phpMyAdmin 5.0.1 documentation" href="index.html" />
    <link rel="up" title="User Guide" href="user.html" />
    <link rel="next" title="Bookmarks" href="bookmarks.html" />
    <link rel="prev" title="Two-factor authentication" href="two_factor.html" /> 
  </head>
  <body role="document">
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="bookmarks.html" title="Bookmarks"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="two_factor.html" title="Two-factor authentication"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.0.1 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="user.html" accesskey="U">User Guide</a> &#187;</li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="transformations">
<span id="id1"></span><h1>Transformations<a class="headerlink" href="#transformations" title="Permalink to this headline">¶</a></h1>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p class="last">You need to have configured the <a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a> for using transformations
feature.</p>
</div>
<div class="section" id="introduction">
<span id="transformationsintro"></span><h2>Introduction<a class="headerlink" href="#introduction" title="Permalink to this headline">¶</a></h2>
<p>To enable transformations, you have to setup the <code class="docutils literal"><span class="pre">column_info</span></code>
table and the proper directives. Please see the <a class="reference internal" href="config.html#config"><span class="std std-ref">Configuration</span></a> on how to do so.</p>
<p>You can apply different transformations to the contents of each
column. The transformation will take the content of each column and
transform it with certain rules defined in the selected
transformation.</p>
<p>Say you have a column &#8216;filename&#8217; which contains a filename. Normally
you would see in phpMyAdmin only this filename. Using transformations
you can transform that filename into a HTML link, so you can click
inside of the phpMyAdmin structure on the column&#8217;s link and will see
the file displayed in a new browser window. Using transformation
options you can also specify strings to append/prepend to a string or
the format you want the output stored in.</p>
<p>For a general overview of all available transformations and their
options, you can consult your <em>&lt;www.your-host.com&gt;/&lt;your-install-
dir&gt;/transformation_overview.php</em> installation.</p>
<p>For a tutorial on how to effectively use transformations, see our
<a class="reference external" href="https://www.phpmyadmin.net/docs/">Link section</a> on the
official phpMyAdmin homepage.</p>
</div>
<div class="section" id="usage">
<span id="transformationshowto"></span><h2>Usage<a class="headerlink" href="#usage" title="Permalink to this headline">¶</a></h2>
<p>Go to your <em>tbl_structure.php</em> page (i.e. reached through clicking on
the &#8216;Structure&#8217; link for a table). There click on &#8220;Change&#8221; (or change
icon) and there you will see three new fields at the end of the line.
They are called &#8216;MIME-type&#8217;, &#8216;Browser transformation&#8217; and
&#8216;Transformation options&#8217;.</p>
<ul class="simple">
<li>The field &#8216;MIME-type&#8217; is a drop-down field. Select the MIME-type that
corresponds to the column&#8217;s contents. Please note that transformations
are inactive as long as no MIME-type is selected.</li>
<li>The field &#8216;Browser transformation&#8217; is a drop-down field. You can
choose from a hopefully growing amount of pre-defined transformations.
See below for information on how to build your own transformation.
There are global transformations and mimetype-bound transformations.
Global transformations can be used for any mimetype. They will take
the mimetype, if necessary, into regard. Mimetype-bound
transformations usually only operate on a certain mimetype. There are
transformations which operate on the main mimetype (like &#8216;image&#8217;),
which will most likely take the subtype into regard, and those who
only operate on a specific subtype (like &#8216;image/jpeg&#8217;). You can use
transformations on mimetypes for which the function was not defined
for. There is no security check for you selected the right
transformation, so take care of what the output will be like.</li>
<li>The field &#8216;Transformation options&#8217; is a free-type textfield. You have
to enter transform-function specific options here. Usually the
transforms can operate with default options, but it is generally a
good idea to look up the overview to see which options are necessary.
Much like the ENUM/SET-Fields, you have to split up several options
using the format &#8216;a&#8217;,&#8217;b&#8217;,&#8217;c&#8217;,...(NOTE THE MISSING BLANKS). This is
because internally the options will be parsed as an array, leaving the
first value the first element in the array, and so forth. If you want
to specify a MIME character set you can define it in the
transformation_options. You have to put that outside of the pre-
defined options of the specific mime-transform, as the last value of
the set. Use the format &#8220;&#8217;; charset=XXX&#8217;&#8221;. If you use a transform, for
which you can specify 2 options and you want to append a character
set, enter &#8220;&#8216;first parameter&#8217;,&#8217;second parameter&#8217;,&#8217;charset=us-ascii&#8217;&#8221;.
You can, however use the defaults for the parameters: &#8220;&#8217;&#8216;,&#8217;&#8216;,&#8217;charset
=us-ascii&#8217;&#8221;. The default options can be configured using
<span class="target" id="index-0"></span><a class="reference internal" href="config.html#cfg_DefaultTransformations"><code class="xref config config-option docutils literal"><span class="pre">$cfg['DefaultTransformations']</span></code></a></li>
</ul>
</div>
<div class="section" id="file-structure">
<span id="transformationsfiles"></span><h2>File structure<a class="headerlink" href="#file-structure" title="Permalink to this headline">¶</a></h2>
<p>All specific transformations for mimetypes are defined through class
files in the directory &#8216;libraries/classes/Plugins/Transformations/&#8217;. Each of
them extends a certain transformation abstract class declared in
libraries/classes/Plugins/Transformations/Abs.</p>
<p>They are stored in files to ease up customization and easy adding of
new transformations.</p>
<p>Because the user cannot enter own mimetypes, it is kept sure that
transformations always work. It makes no sense to apply a
transformation to a mimetype the transform-function doesn&#8217;t know to
handle.</p>
<p>There is a file called &#8216;<em>transformations.lib.php</em>&#8216; that provides some
basic functions which can be included by any other transform function.</p>
<p>The file name convention is <code class="docutils literal"><span class="pre">[Mimetype]_[Subtype]_[Transformation</span>
<span class="pre">Name].class.php</span></code>, while the abtract class that it extends has the
name <code class="docutils literal"><span class="pre">[Transformation</span> <span class="pre">Name]TransformationsPlugin</span></code>. All of the
methods that have to be implemented by a transformations plug-in are:</p>
<ol class="arabic simple">
<li>getMIMEType() and getMIMESubtype() in the main class;</li>
<li>getName(), getInfo() and applyTransformation() in the abstract class
it extends.</li>
</ol>
<p>The getMIMEType(), getMIMESubtype() and getName() methods return the
name of the MIME type, MIME Subtype and transformation accordingly.
getInfo() returns the transformation&#8217;s description and possible
options it may receive and applyTransformation() is the method that
does the actual work of the transformation plug-in.</p>
<p>Please see the libraries/classes/Plugins/Transformations/TEMPLATE and
libraries/classes/Plugins/Transformations/TEMPLATE_ABSTRACT files for adding
your own transformation plug-in. You can also generate a new
transformation plug-in (with or without the abstract transformation
class), by using
<code class="file docutils literal"><span class="pre">scripts/transformations_generator_plugin.sh</span></code> or
<code class="file docutils literal"><span class="pre">scripts/transformations_generator_main_class.sh</span></code>.</p>
<p>The applyTransformation() method always gets passed three variables:</p>
<ol class="arabic simple">
<li><strong>$buffer</strong> - Contains the text inside of the column. This is the
text, you want to transform.</li>
<li><strong>$options</strong> - Contains any user-passed options to a transform
function as an array.</li>
<li><strong>$meta</strong> - Contains an object with information about your column. The
data is drawn from the output of the <a class="reference external" href="https://www.php.net/mysql_fetch_field">mysql_fetch_field()</a> function. This means, all
object properties described on the <a class="reference external" href="https://www.php.net/mysql_fetch_field">manual page</a> are available in this
variable and can be used to transform a column accordingly to
unsigned/zerofill/not_null/... properties. The $meta-&gt;mimetype
variable contains the original MIME-type of the column (i.e.
&#8216;text/plain&#8217;, &#8216;image/jpeg&#8217; etc.)</li>
</ol>
</div>
</div>


          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h3><a href="index.html">Table Of Contents</a></h3>
  <ul>
<li><a class="reference internal" href="#">Transformations</a><ul>
<li><a class="reference internal" href="#introduction">Introduction</a></li>
<li><a class="reference internal" href="#usage">Usage</a></li>
<li><a class="reference internal" href="#file-structure">File structure</a></li>
</ul>
</li>
</ul>

  <h4>Previous topic</h4>
  <p class="topless"><a href="two_factor.html"
                        title="previous chapter">Two-factor authentication</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="bookmarks.html"
                        title="next chapter">Bookmarks</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/transformations.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3>Quick search</h3>
    <form class="search" action="search.html" method="get">
      <div><input type="text" name="q" /></div>
      <div><input type="submit" value="Go" /></div>
      <input type="hidden" name="check_keywords" value="yes" />
      <input type="hidden" name="area" value="default" />
    </form>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="bookmarks.html" title="Bookmarks"
             >next</a> |</li>
        <li class="right" >
          <a href="two_factor.html" title="Two-factor authentication"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.0.1 documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="user.html" >User Guide</a> &#187;</li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2018, The phpMyAdmin devel team.
      Created using <a href="http://sphinx-doc.org/">Sphinx</a> 1.4.9.
    </div>
  </body>
</html>